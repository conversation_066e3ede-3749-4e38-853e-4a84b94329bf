-- mqtt_module.lua - Simple MQTT client for Air780EG/Air780E
-- Uses the global mqtt module directly

-- Required modules
local sys = require("sys")
local PinModule = require("pin_module")

-- Create a simple log function if the log module is not available
if not log then
    _G.log = {
        info = function(tag, ...) print("[INFO][" .. tag .. "]", ...) end,
        warn = function(tag, ...) print("[WARN][" .. tag .. "]", ...) end,
        error = function(tag, ...) print("[ERROR][" .. tag .. "]", ...) end,
        debug = function(tag, ...) print("[DEBUG][" .. tag .. "]", ...) end
    }
end

local mqtt_module = {}

-- MQTT configuration
local mqtt_server = "45.76.188.38"
local mqtt_port = 1883
local mqtt_username = "admin"
local mqtt_password = "public"
local mqtt_clientid = nil  -- Will be set to IMEI

-- MQTT state variables
local mqtt_client = nil
local mqtt_ready = false
local mqtt_payload = nil
local retry_count = 0

-- Get device IMEI (or other unique identifier)
local function getDeviceId()
    -- Try to get IMEI from mobile module (preferred method)
    if mobile and mobile.imei then
        local imei = mobile.imei()
        if imei and imei ~= "" then
            log.info("MQTT", "Got IMEI from mobile module:", imei)
            return imei
        end
    end

    -- Fallback to a hardcoded ID with timestamp
    local fallback_id = "air780e_" .. os.time()
    log.info("MQTT", "Using fallback ID:", fallback_id)
    return fallback_id
end

-- MQTT connection task
local function mqtt_task()
    log.info("MQTT", "Starting MQTT task")

    -- Wait for network to be ready
    sys.waitUntil("IP_READY", 30000)

    while true do
        mqtt_ready = false

        -- Make sure we have a client ID
        if mqtt_clientid == nil then
            mqtt_clientid = getDeviceId()
        end

        log.info("MQTT", "Connecting to MQTT server", mqtt_server, "port", mqtt_port)
        log.info("MQTT", "Using client ID:", mqtt_clientid)

        -- Beep to indicate MQTT connection attempt (more audible)
        sys.taskInit(function()
            PinModule.beepPattern(3, 200, 300, 2800)  -- 3 longer beeps at 2.8kHz with longer pauses
            sys.wait(500)  -- Wait before proceeding to connection
        end)

        -- Create MQTT client
        local success, result = pcall(function()
            -- Try with socket.LWIP_GP first (cellular network)
            if socket and socket.LWIP_GP then
                return mqtt.create(socket.LWIP_GP, mqtt_server, mqtt_port)
            else
                -- If that fails, try with nil (default adapter)
                return mqtt.create(nil, mqtt_server, mqtt_port)
            end
        end)

        if not success or not result then
            log.error("MQTT", "Failed to create MQTT client:", result or "unknown error")
            sys.wait(5000)
            goto continue
        end

        mqtt_client = result

        -- Set up authentication
        mqtt_client:auth(mqtt_clientid, mqtt_username, mqtt_password)

        -- Set keep-alive interval (60 seconds)
        mqtt_client:keepalive(60)

        -- Set up MQTT callback
        mqtt_client:on(function(client, event, data, payload)
            if event == "conack" then
                -- Connected and authenticated
                log.info("MQTT", "Connected to MQTT server")
                mqtt_ready = true
                retry_count = 0

                -- Publish the MQTT_CONNECTED event
                sys.publish("MQTT_CONNECTED")

                -- Subscribe to our topic (IMEI)
                sys.timerStart(function()
                    if mqtt_client and mqtt_client:ready() then
                        local result = mqtt_client:subscribe(mqtt_clientid, 0)
                        if result then
                            log.info("MQTT", "Subscribed to topic:", mqtt_clientid)

                            -- Publish online status
                            sys.timerStart(function()
                                if mqtt_client and mqtt_client:ready() then
                                    mqtt_client:publish(mqtt_clientid .. "/msg", '{"status":"online"}', 0, 0)
                                    log.info("MQTT", "Published online status")

                                    -- Publish initial sensor data
                                    sys.timerStart(function()
                                        if mqtt_client and mqtt_client:ready() then
                                            -- Trigger the initial data publish event
                                            sys.publish("MQTT_PUBLISH_INITIAL_DATA")
                                            log.info("MQTT", "Triggered initial data publish")
                                        end
                                    end, 1000)
                                end
                            end, 500)
                        else
                            log.error("MQTT", "Failed to subscribe to topic:", mqtt_clientid)
                        end
                    end
                end, 1000)

            elseif event == "recv" then
                -- Received message
                log.info("MQTT", "Received message on topic:", data, "payload:", payload)
                mqtt_payload = payload
                sys.publish("MQTT_MESSAGE_RECEIVED", data, payload)

            elseif event == "sent" then
                -- Message sent successfully
                log.info("MQTT", "Message sent, ID:", data)
                sys.publish("MQTT_SENT", data)

            elseif event == "disconnect" then
                -- Disconnected from server
                log.warn("MQTT", "Disconnected from MQTT server")
                mqtt_ready = false

                -- Publish the MQTT_DISCONNECTED event
                sys.publish("MQTT_DISCONNECTED")

                -- Increment retry counter for exponential backoff
                retry_count = retry_count + 1

                -- Trigger reconnection
                sys.publish("MQTT_RECONNECT")

            elseif event == "pong" then
                -- Received ping response
                log.debug("MQTT", "Received MQTT ping response")
            end
        end)

        -- Wait a moment for the connection attempt beeps to finish
        sys.wait(1000)

        -- Connect to MQTT server
        log.info("MQTT", "Connecting to MQTT server...")

        -- Beep to indicate actual connection attempt (more distinct)
        sys.taskInit(function()
            PinModule.beep(1500, 400)  -- Single lower-pitched longer beep at 1.5kHz for 400ms
        end)

        local connect_result = mqtt_client:connect()

        if not connect_result then
            log.error("MQTT", "Failed to start MQTT connection")
            mqtt_client:close()
            mqtt_client = nil

            -- Wait before retrying with exponential backoff
            local wait_time = math.min(2 ^ retry_count, 60)
            log.info("MQTT", "Waiting", wait_time, "seconds before retrying...")
            sys.wait(wait_time * 1000)
        else
            -- Wait for disconnect event
            sys.waitUntil("MQTT_RECONNECT")

            -- Close the client if it exists
            if mqtt_client then
                mqtt_client:close()
                mqtt_client = nil
            end

            -- Wait before reconnecting with exponential backoff
            local wait_time = math.min(2 ^ retry_count, 60)
            log.info("MQTT", "Waiting", wait_time, "seconds before reconnecting...")
            sys.wait(wait_time * 1000)
        end

        ::continue::
    end
end

-- Initialize the module
function mqtt_module.init()
    -- Start the MQTT task
    sys.taskInit(mqtt_task)
    return true
end

-- Publish a message to a topic
function mqtt_module.publish(data, topic)
    -- If topic is not provided, use the default topic (IMEI/msg)
    if not topic then
        topic = mqtt_clientid .. "/msg"
    end

    -- Check if client is ready
    if not mqtt_client or not mqtt_ready then
        log.warn("MQTT", "Client not ready, cannot publish")
        return false
    end

    -- Publish the message
    local success, result = pcall(function()
        return mqtt_client:publish(topic, data, 0, 0)
    end)

    if success and result then
        log.info("MQTT", "Published message to topic:", topic)
        return true
    else
        log.error("MQTT", "Failed to publish message:", result or "unknown error")
        return false
    end
end

-- Get the last received message
function mqtt_module.get_last_message()
    return mqtt_payload
end

-- Clear the last received message
function mqtt_module.clear_message()
    mqtt_payload = nil
end

-- Check if MQTT client is ready
function mqtt_module.is_ready()
    return mqtt_ready
end

-- Get the client ID (IMEI)
function mqtt_module.get_client_id()
    return mqtt_clientid
end

-- Get the default publish topic (IMEI/msg)
function mqtt_module.get_publish_topic()
    return mqtt_clientid .. "/msg"
end

-- Get the default subscribe topic (IMEI)
function mqtt_module.get_subscribe_topic()
    return mqtt_clientid
end

-- Return the module
return mqtt_module
