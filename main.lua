-- main.lua - Application for Air780EG with MQTT, AHT20 sensor, and GPS
-- This script reads sensor data and publishes it via MQTT

PROJECT = "mqtt_sensor_demo"
VERSION = "1.3.5"

-- Load required modules
local sys = require("sys")
local PinModule = require("pin_module")

-- Try to load the MQTT module
local mqtt_module_available, mqtt_module = pcall(require, "mqtt_module")
if not mqtt_module_available then
    print("Warning: MQTT module could not be loaded. MQTT functionality will be disabled.")
    -- Create a dummy mqtt_module with empty functions
    mqtt_module = {
        init = function() return false end,
        publish = function() return false end,
        is_ready = function() return false end,
        get_client_id = function() return "unknown" end,
        get_publish_topic = function() return "unknown/msg" end,
        get_subscribe_topic = function() return "unknown" end
    }
end

-- Configuration
local PRINT_INTERVAL = 5000  -- Print sensor data every 5 seconds
local MQTT_PUBLISH_INTERVAL = 60000  -- Publish MQTT data every 60 seconds

-- AHT20 sensor configuration
local AHT20 = {
    i2c_id = 0,           -- I2C bus ID
    addr = 0x38,           -- AHT20 I2C address
    cmd_init = 0xBE,       -- Initialization command
    cmd_measure = 0xAC,    -- Trigger measurement command
    cmd_reset = 0xBA,      -- Soft reset command
    temp_offset = -14.6    -- Temperature calibration offset (40.6 - 26 = 14.6°C too high)
}

-- GPS module
local GPS = require("libgnss_gps")

-- ADC configuration
local ADC_CONFIG = {
    id = 1,                -- ADC ID (ADC1 on pin 96 on Air780EG)
    channel = 0,           -- Using channel 0 for ADC1
    reference_voltage = 3.3, -- Reference voltage in volts
    scaling_factor = 5.7   -- Voltage conversion factor (voltage * 5.7 / 1000)
}

-- Initialize log module if available
if log and log.info then
    log.info("Main", "Starting application version", VERSION)
else
    -- Create a simple log function if the log module is not available
    _G.log = {
        info = function(tag, ...) print("[INFO][" .. tag .. "]", ...) end,
        warn = function(tag, ...) print("[WARN][" .. tag .. "]", ...) end,
        error = function(tag, ...) print("[ERROR][" .. tag .. "]", ...) end,
        debug = function(tag, ...) print("[DEBUG][" .. tag .. "]", ...) end
    }
    log.info("Main", "Log module not found, using simple logging")
    log.info("Main", "Starting application version", VERSION)
end

-- Initialize JSON module if not available
if not json then
    log.warn("Main", "JSON module not found, loading simple JSON")
    -- Simple JSON encoder for sensor data
    _G.json = {
        encode = function(obj)
            local function serializeTable(val, indent)
                indent = indent or ""
                local res = "{"
                for k, v in pairs(val) do
                    if type(k) == "string" then
                        res = res .. '"' .. k .. '":'
                    else
                        res = res .. k .. ":"
                    end

                    if type(v) == "table" then
                        res = res .. serializeTable(v, indent .. "  ")
                    elseif type(v) == "string" then
                        res = res .. '"' .. v .. '"'
                    else
                        res = res .. tostring(v)
                    end
                    res = res .. ","
                end
                if res ~= "{" then
                    res = res:sub(1, -2)  -- Remove trailing comma
                end
                return res .. "}"
            end

            if type(obj) == "table" then
                return serializeTable(obj)
            elseif type(obj) == "string" then
                return '"' .. obj .. '"'
            else
                return tostring(obj)
            end
        end
    }
end

-- Initialize AHT20 sensor
local function initAHT20()
    print("\n=== AHT20 Sensor Initialization Start ===")

    -- Initialize I2C with 100kHz speed
    local setup_result = i2c.setup(AHT20.i2c_id, 100000)
    print("I2C setup result: " .. tostring(setup_result))

    if setup_result ~= 1 then
        print("Error: Failed to initialize I2C bus")
        return false
    end

    -- Scan for I2C devices
    print("Scanning I2C bus for devices...")
    local found = false

    -- Try to communicate with AHT20
    local result = i2c.send(AHT20.i2c_id, AHT20.addr, {0x00})
    if result then
        print("Found AHT20 sensor at address 0x" .. string.format("%02X", AHT20.addr))
        found = true
    else
        print("AHT20 sensor not found at address 0x" .. string.format("%02X", AHT20.addr))
        return false
    end

    -- Soft reset the sensor
    i2c.send(AHT20.i2c_id, AHT20.addr, {AHT20.cmd_reset})
    sys.wait(20)  -- Wait 20ms for reset to complete

    -- Initialize the sensor
    i2c.send(AHT20.i2c_id, AHT20.addr, {AHT20.cmd_init, 0x08, 0x00})
    sys.wait(10)  -- Wait 10ms for initialization

    -- Read status to verify initialization
    local status = i2c.recv(AHT20.i2c_id, AHT20.addr, 1)
    if not status or #status ~= 1 then
        print("Error: Failed to read AHT20 status")
        return false
    end

    -- Check if calibration bit (bit 3) is set
    if (status:byte(1) & 0x08) == 0 then
        print("Error: AHT20 calibration bit not set")
        return false
    end

    print("AHT20 sensor initialized successfully")
    print("=== AHT20 Initialization Complete ===\n")
    return true
end

-- Read temperature and humidity from AHT20
local function readAHT20()
    -- Trigger measurement
    local send_result = i2c.send(AHT20.i2c_id, AHT20.addr, {AHT20.cmd_measure, 0x33, 0x00})
    if not send_result then
        print("Error: Failed to send measurement command")
        return nil
    end

    -- Wait for measurement to complete (at least 80ms)
    sys.wait(80)

    -- Read 6 bytes of data (status + humidity + temperature)
    local data = i2c.recv(AHT20.i2c_id, AHT20.addr, 6)
    if not data or #data ~= 6 then
        print("Error: Failed to read AHT20 data")
        return nil
    end

    -- Check if busy bit (bit 7) is clear
    if (data:byte(1) & 0x80) ~= 0 then
        print("Error: AHT20 still busy")
        return nil
    end

    -- Extract humidity (20 bits) [1:5]
    local humidity_raw = ((data:byte(2) & 0xFF) << 12) | ((data:byte(3) & 0xFF) << 4) | ((data:byte(4) & 0xF0) >> 4)
    local humidity = (humidity_raw / 1048576.0) * 100.0  -- Convert to percentage

    -- Extract temperature (20 bits) [5:7]
    local temp_raw = ((data:byte(4) & 0x0F) << 16) | ((data:byte(5) & 0xFF) << 8) | (data:byte(6) & 0xFF)
    local temperature = ((temp_raw / 1048576.0) * 200.0) - 50.0  -- Convert to Celsius

    -- Apply calibration offset
    temperature = temperature + AHT20.temp_offset

    return {
        temperature = temperature,
        humidity = humidity,
        raw_temperature = temperature - AHT20.temp_offset,
        raw_humidity = humidity
    }
end

-- Initialize GPS
local function initGPS()
    print("\n=== GPS Initialization Start ===")

    -- Make sure the GPS module is loaded
    if not GPS then
        print("Error: GPS module not loaded")
        print("=== GPS Initialization Failed ===\n")
        return false
    end

    -- Use the libgnss_gps module's init function
    local success = GPS.init()

    if success then
        print("GPS initialized successfully using libgnss")

        -- Start a task to monitor GPS status
        sys.taskInit(function()
            -- Wait for system to stabilize
            sys.wait(5000)

            -- Periodically check GPS status
            while true do
                -- Use pcall to catch any errors
                local success, result = pcall(function()
                    local sat_info = GPS.getSatelliteInfo()
                    if sat_info then
                        print("GPS Status: Satellites: " .. sat_info.satellites ..
                              ", Fix quality: " .. sat_info.fix_quality ..
                              ", HDOP: " .. sat_info.hdop)
                    else
                        print("GPS Status: No satellite info available")
                    end

                    -- Check if we have a position
                    if GPS.hasFix() then
                        local pos = GPS.getPosition()
                        if pos then
                            print("GPS Position: " .. pos.latitude .. "N, " .. pos.longitude .. "E")
                            print("Speed: " .. pos.speed .. " km/h")
                        end
                    else
                        print("GPS Position: No fix yet")
                    end
                end)

                if not success then
                    print("Error in GPS monitoring task: " .. tostring(result))
                end

                -- Wait before checking again
                sys.wait(30000)  -- Check every 30 seconds
            end
        end)

        print("=== GPS Initialization Complete ===\n")
        return true
    else
        print("Error: Failed to initialize GPS")
        print("=== GPS Initialization Failed ===\n")
        return false
    end
end

-- Get GPS position
local function getGPSPosition()
    -- Use the libgnss_gps module's getPosition function
    return GPS.getPosition()
end

-- Initialize ADC
local function initADC()
    print("\n=== ADC Initialization Start ===")

    -- Initialize ADC
    print("Initializing ADC" .. ADC_CONFIG.id .. " channel " .. ADC_CONFIG.channel)
    local result = adc.open(ADC_CONFIG.id, ADC_CONFIG.channel)
    print("ADC open result: " .. tostring(result))

    -- In LuatOS, adc.open() may return true instead of 1
    if result == true or result == 1 then
        print("ADC initialized successfully")
        print("=== ADC Initialization Complete ===\n")
        return true
    else
        print("Error: Failed to initialize ADC")
        return false
    end
end

-- Read voltage from ADC
local function readVoltage()
    -- Read from ADC
    local adcval, voltage_mv = adc.read(ADC_CONFIG.id, ADC_CONFIG.channel)

    if not voltage_mv then
        print("Error: Failed to read ADC voltage")
        return nil
    end

    -- Convert millivolts to volts using the voltage factor formula
    -- Formula: voltage * voltage_factor / 1000
    local voltage = (voltage_mv * ADC_CONFIG.scaling_factor) / 1000.0

    print("ADC raw value: " .. adcval .. ", voltage_mv: " .. voltage_mv .. "mV")
    return voltage
end

-- Get mobile signal strength (RSSI)
local function getRSSI()
    -- Make sure mobile module is initialized
    if not mobile then
        print("Mobile module not available")
        return 0
    end

    -- Try to get signal strength using mobile.signal()
    if mobile.signal then
        local rssi = mobile.signal()
        if rssi then
            return rssi
        end
    end

    -- Alternative method for Air780EG
    if mobile.csq then
        local csq = mobile.csq()
        if csq then
            -- Convert CSQ to dBm: CSQ=0 -> -113dBm, CSQ=31 -> -51dBm
            return -113 + (csq * 2)
        end
    end

    return 0
end

-- Format sensor data as JSON
local function formatSensorData(temp, humidity, voltage, gps, rssi)
    -- Format GPS coordinates from DDMM.MMMMM to DD.DDDDD
    local lat_str = "0 N"  -- Default value
    local lon_str = "0 E" -- Default value

    if gps and gps.latitude and gps.longitude then
        -- Convert latitude from DDMM.MMMMM format to DD.DDDDD format
        local lat_deg = math.floor(gps.latitude / 100)
        local lat_min = gps.latitude - (lat_deg * 100)
        local lat_decimal = lat_deg + (lat_min / 60)
        lat_str = string.format("%.5f N", lat_decimal)

        -- Convert longitude from DDDMM.MMMMM format to DDD.DDDDD format
        local lon_deg = math.floor(gps.longitude / 100)
        local lon_min = gps.longitude - (lon_deg * 100)
        local lon_decimal = lon_deg + (lon_min / 60)
        lon_str = string.format("%.5f E", lon_decimal)
    end

    -- Format exactly as specified with fields in exact order
    local data = {
        Lon = lon_str,
        Lat = lat_str,
        hum = math.floor(humidity or 29),  -- Default to 29 if not available
        ver = VERSION,
        rssi = rssi or 27,  -- Default to 27 if not available
        volt = voltage or 12.1923,  -- Default to 12.1923 if not available
        Speed = gps and gps.speed or 0,
        temp = math.floor(temp or 30),  -- Default to 30 if not available
        motion = 0,  -- Placeholder as in example
        light = 0    -- Placeholder as in example
    }

    -- Use json.encode with a custom function to ensure field order
    local json_str = "{"
    json_str = json_str .. "\"Lon\":\"" .. data.Lon .. "\","
    json_str = json_str .. "\"Lat\":\"" .. data.Lat .. "\","
    json_str = json_str .. "\"hum\":" .. data.hum .. ","
    json_str = json_str .. "\"ver\":\"" .. data.ver .. "\","
    json_str = json_str .. "\"rssi\":" .. data.rssi .. ","
    json_str = json_str .. "\"volt\":" .. data.volt .. ","
    json_str = json_str .. "\"Speed\":" .. data.Speed .. ","
    json_str = json_str .. "\"temp\":" .. data.temp .. ","
    json_str = json_str .. "\"motion\":" .. data.motion .. ","
    json_str = json_str .. "\"light\":" .. data.light .. ""
    json_str = json_str .. "}"

    return json_str
end

-- Task to read and print sensor data
local function sensorTask()
    print("Starting sensor reading task")

    -- Wait for system to stabilize
    sys.wait(2000)

    while true do
        -- Read temperature and humidity
        local temp_hum = readAHT20()
        local temperature = temp_hum and temp_hum.temperature or 25.0
        local humidity = temp_hum and temp_hum.humidity or 50.0

        -- Read voltage
        local voltage = readVoltage() or 12.0

        -- Read GPS position
        local gps = getGPSPosition()

        -- Get RSSI
        local rssi = getRSSI() or 0

        -- Format data as JSON
        local json_data = formatSensorData(temperature, humidity, voltage, gps, rssi)

        -- Print sensor data
        print("\n=== SENSOR DATA ===")
        print(json_data)
        print("===================\n")

        -- Print individual values
        print("Temperature: " .. string.format("%.1f", temperature) .. "°C")
        print("Humidity: " .. string.format("%.1f", humidity) .. "%")
        print("Voltage: " .. string.format("%.2f", voltage) .. "V")
        print("Signal Strength (RSSI): " .. rssi .. " dBm")

        -- Always display satellite info, even without a fix
        local sat_info = GPS.getSatelliteInfo()
        print("GPS Status: " .. (sat_info.has_fix and "Fix acquired" or "No fix yet"))
        print("Satellites: " .. sat_info.satellites)
        print("Fix Quality: " .. sat_info.fix_quality .. " (0=No fix, 1=GPS, 2=DGPS)")
        print("HDOP: " .. sat_info.hdop .. " (lower is better)")

        if gps then
            print("GPS Coordinates: " .. string.format("%.5f", gps.latitude) .. " N, " ..
                  string.format("%.5f", gps.longitude) .. " E")
            print("Speed: " .. string.format("%.1f", gps.speed) .. " km/h")
            print("Timestamp: " .. os.date("%Y-%m-%d %H:%M:%S", gps.timestamp))
        end

        -- Display GPS raw data for debugging
        print("\n=== GPS Data ===")

        -- Try to get raw GPS data
        local raw_data = GPS.getRawData()
        if raw_data then
            print("GPS Fix Status: " .. (raw_data.hasFix and "YES" or "NO"))
            print("Satellites: " .. raw_data.satellites)
            print("Fix Quality: " .. raw_data.fix_quality)
            print("HDOP: " .. raw_data.hdop)

            -- Display RMC data if available
            if raw_data.rmc then
                print("\nRMC Data (Position):")
                local rmc = raw_data.rmc
                print("  Valid: " .. tostring(rmc.valid))
                if rmc.valid then
                    print("  Latitude: " .. rmc.lat .. " (" .. (rmc.lat / 100) .. " degrees)")
                    print("  Longitude: " .. rmc.lng .. " (" .. (rmc.lng / 100) .. " degrees)")
                    print("  Speed: " .. rmc.speed .. " knots (" .. (rmc.speed * 1.852) .. " km/h)")
                    print("  Course: " .. rmc.course .. " degrees")
                    print("  Date: " .. rmc.year .. "-" .. rmc.month .. "-" .. rmc.day)
                    print("  Time: " .. rmc.hour .. ":" .. rmc.min .. ":" .. rmc.sec)
                end
            end

            -- Display GGA data if available
            if raw_data.gga then
                print("\nGGA Data (Fix):")
                local gga = raw_data.gga
                print("  Quality: " .. (gga.quality or 0) .. " (0=No fix, 1=GPS, 2=DGPS)")
                print("  Satellites: " .. (gga.satellites or 0))
                print("  HDOP: " .. (gga.hdop or 0))
                print("  Altitude: " .. (gga.altitude or 0) .. " meters")
            end

            -- Display GSA data if available
            if raw_data.gsa then
                print("\nGSA Data (Satellites):")
                local gsa = raw_data.gsa
                print("  Mode: " .. (gsa.mode or "unknown") .. " (1=No fix, 2=2D fix, 3=3D fix)")
                print("  PDOP: " .. (gsa.pdop or 0))
                print("  HDOP: " .. (gsa.hdop or 0))
                print("  VDOP: " .. (gsa.vdop or 0))
            end
        else
            print("No raw GPS data available")
        end

        print("=== End GPS Data ===")

        -- Wait for the next reading interval
        sys.wait(PRINT_INTERVAL)
    end
end

-- Initialize mobile network
local function initMobile()
    print("\n=== Mobile Network Initialization Start ===")

    -- Check if mobile module is available
    if not mobile then
        print("Mobile module not available")
        return false
    end

    -- Wait for network registration
    print("Waiting for network registration...")
    local timeout = 30  -- 30 seconds timeout
    local start_time = os.time()

    while os.time() - start_time < timeout do
        -- Check network registration status
        if mobile.status then
            local status = mobile.status()
            print("Network status: " .. tostring(status))

            -- Different versions of LuatOS may return different status formats
            -- Some return strings like "REGISTERED", others return numbers like 1 or 2
            if status == "REGISTERED" or status == "REGISTERED_ROAMING" or
               status == 1 or status == 2 or status == 5 then
                print("Network registered successfully")

                -- Get signal strength
                local rssi = getRSSI()
                print("Signal strength (RSSI): " .. rssi .. " dBm")

                -- Get operator info if available
                if mobile.getOperator then
                    local operator = mobile.getOperator()
                    print("Network operator: " .. (operator or "Unknown"))
                end

                print("Mobile network initialized successfully")
                print("=== Mobile Network Initialization Complete ===\n")
                return true
            end
        end

        sys.wait(1000)  -- Check every second
    end

    -- Even if registration times out, we can still try to get signal strength
    local rssi = getRSSI()
    if rssi and rssi ~= 0 then
        print("Network registration timed out, but signal detected (RSSI: " .. rssi .. " dBm)")
        print("Mobile network partially initialized")
        print("=== Mobile Network Initialization Complete with Warnings ===\n")
        return true
    end

    print("Warning: Network registration timeout")
    print("=== Mobile Network Initialization Complete with Warnings ===\n")
    return false
end

-- Function to publish sensor data to MQTT
-- This function starts a task to publish data and returns immediately
local function publishSensorData()
    -- Start a task to handle the publishing (allows yielding)
    sys.taskInit(function()
        -- Check if MQTT is connected
        if mqtt_module.is_ready() then
            -- Read sensor data
            local temp_hum = readAHT20()
            local temperature = temp_hum and temp_hum.temperature or 25.0
            local humidity = temp_hum and temp_hum.humidity or 50.0
            local voltage = readVoltage() or 12.0
            local gps = getGPSPosition()
            local rssi = getRSSI() or 0

            -- Format and publish sensor data
            local json_data = formatSensorData(temperature, humidity, voltage, gps, rssi)
            local publish_result = mqtt_module.publish(json_data)

            if publish_result then
                print("Published sensor data to MQTT")
                -- Publish a success event that other parts of the code can listen for
                sys.publish("MQTT_PUBLISH_SUCCESS")
            else
                print("Failed to publish sensor data to MQTT")
                -- Publish a failure event that other parts of the code can listen for
                sys.publish("MQTT_PUBLISH_FAILURE")
            end
        else
            print("MQTT not connected, skipping publish")
            -- Publish a failure event that other parts of the code can listen for
            sys.publish("MQTT_PUBLISH_FAILURE")
        end
    end)

    -- Return true to indicate that the task was started successfully
    return true
end

-- MQTT message handler
local function handleMQTTMessage(topic, payload)
    print("Received MQTT message on topic:", topic)
    print("Payload:", payload)

    -- Try to parse the payload as JSON
    local success, data = pcall(json.decode, payload)
    if success and data then
        -- Check if this is a command message
        if data.command then
            print("Received command:", data.command)

            -- Handle different commands
            if data.command == "check" then
                -- Publish sensor data using the common function
                -- This will start a task internally
                if publishSensorData() then
                    print("Started task to send sensor data in response to check command")
                else
                    print("Failed to start task for sending sensor data in response to check command")
                end
            -- UART-related code commented out for testing
            --[[
            elseif data.command == "uart1" then
                -- Check UART1 for data at 9600 baud rate
                sys.taskInit(function()
                    print("Checking UART1 for data at 9600 baud rate")

                    -- Setup UART1 with 9600 baud rate if not already set up
                    uart.setup(1, 9600, 8, 1, uart.PARITY_NONE, uart.FLOW_NONE)

                    -- Wait a moment for data to arrive
                    sys.wait(500)

                    -- Check if data is available
                    local data_len = uart.rx_len(1)
                    if data_len and data_len > 0 then
                        -- Read the data
                        local data = uart.read(1, data_len)
                        if data then
                            print("UART1 data received (" .. data_len .. " bytes):")
                            -- Print as hex
                            local hex_str = ""
                            for i = 1, #data do
                                hex_str = hex_str .. string.format("%02X ", string.byte(data, i))
                            end
                            print("HEX: " .. hex_str)
                            -- Print as ASCII if printable
                            print("ASCII: " .. data)

                            -- Publish the data via MQTT
                            local response = {
                                command = "uart1",
                                status = "success",
                                data_hex = hex_str,
                                data_ascii = data,
                                data_length = data_len
                            }
                            mqtt_module.publish(json.encode(response))
                        else
                            print("Failed to read data from UART1")
                            mqtt_module.publish(json.encode({command = "uart1", status = "error", message = "Failed to read data"}))
                        end
                    else
                        print("No data available on UART1")
                        mqtt_module.publish(json.encode({command = "uart1", status = "info", message = "No data available"}))
                    end
                end)
            --]]
            elseif data.command == "allon" then
                print("Turning all outputs on")
                -- Turn on all relays (no yielding needed)
                PinModule.relayControl("Relay1", 1)
                PinModule.relayControl("Relay2", 1)
                PinModule.relayControl("Relay3", 1)
                PinModule.relayControl("Out3", 1)

                -- Send sensor data as confirmation (same as check command)
                if publishSensorData() then
                    print("Started task to send sensor data in response to allon command")
                else
                    print("Failed to start task for sending sensor data in response to allon command")
                end

            elseif data.command == "alloff" then
                print("Turning all outputs off")
                -- Turn off all relays (no yielding needed)
                PinModule.relayControl("Relay1", 0)
                PinModule.relayControl("Relay2", 0)
                PinModule.relayControl("Relay3", 0)
                PinModule.relayControl("Out3", 0)

                -- Send sensor data as confirmation (same as check command)
                if publishSensorData() then
                    print("Started task to send sensor data in response to alloff command")
                else
                    print("Failed to start task for sending sensor data in response to alloff command")
                end
            end
        end
    end
end

-- MQTT initial data publishing handler
local function handleInitialDataPublish()
    print("Publishing initial sensor data to MQTT")
    -- Call publishSensorData which will start a task
    if publishSensorData() then
        print("Started task to publish initial sensor data")
    else
        print("Failed to start task for publishing initial sensor data")
    end
end

-- Initialization task that runs in a coroutine
local function initTask()
    print("\n========================================")
    print("  Air780EG MQTT Sensor Application")
    print("  Version: " .. VERSION)
    print("========================================\n")

    -- Initialize pin module
    PinModule.setupPins()
    print("Pin module initialized")

    -- Initialize AHT20 sensor
    local aht20_success = initAHT20()
    if not aht20_success then
        print("Warning: Failed to initialize AHT20 sensor")
    end

    -- Initialize ADC
    local adc_success = initADC()
    if not adc_success then
        print("Warning: Failed to initialize ADC")
    end

    -- Initialize GPS
    local gps_success = initGPS()
    if not gps_success then
        print("Warning: Failed to initialize GPS")
    end

    -- Initialize mobile network
    local mobile_success = initMobile()
    if not mobile_success then
        print("Warning: Failed to initialize mobile network")
    end

    -- Initialize MQTT module if available
    local mqtt_init_result = mqtt_module.init()
    if mqtt_init_result then
        print("MQTT module initialized successfully")
    else
        print("Warning: MQTT module initialization failed or not available")
    end

    -- Subscribe to MQTT message events
    sys.subscribe("MQTT_MESSAGE_RECEIVED", handleMQTTMessage)

    -- Subscribe to MQTT initial data publish event
    sys.subscribe("MQTT_PUBLISH_INITIAL_DATA", handleInitialDataPublish)

    -- UART1 monitoring task commented out for testing
    --[[
    -- Start a task to monitor UART1 for data
    sys.taskInit(function()
        print("Starting UART1 monitoring task at 9600 baud rate")

        -- Setup UART1 with 9600 baud rate
        uart.setup(1, 9600, 8, 1, uart.PARITY_NONE, uart.FLOW_NONE)

        -- Wait for system to stabilize
        sys.wait(2000)

        -- Continuously check for data
        while true do
            -- Check if data is available
            local data_len = uart.on(1, "receive", function(id, len)
                -- Read the data when it's available
                local data = uart.read(1, len)
                if data then
                    print("\n=== UART1 DATA RECEIVED ===")
                    print("Length: " .. len .. " bytes")

                    -- Print as hex
                    local hex_str = ""
                    for i = 1, #data do
                        hex_str = hex_str .. string.format("%02X ", string.byte(data, i))
                    end
                    print("HEX: " .. hex_str)

                    -- Print as ASCII if printable
                    print("ASCII: " .. data)
                    print("===========================\n")
                end
            end)
        end
    end)
    --]]

    -- Monitor MQTT connection state
    sys.subscribe("MQTT_CONNECTED", function()
        print("MQTT connected!")
        -- Turn on CloudLED when MQTT is connected
        PinModule.setCloudLED(1)
    end)

    sys.subscribe("MQTT_DISCONNECTED", function()
        print("MQTT disconnected!")
        -- Turn off CloudLED when MQTT is disconnected
        PinModule.setCloudLED(0)
    end)

    -- Start the sensor reading task
    sys.taskInit(sensorTask)

    print("Initialization complete")
    print("Printing sensor data every " .. (PRINT_INTERVAL / 1000) .. " seconds")
    print("Publishing to MQTT once when connected and in response to commands")
    print("========================================\n")
end

-- Main application entry point
local function main()
    -- Start the initialization task in a coroutine
    sys.taskInit(initTask)

    -- Keep the system running
    sys.run()
end

-- Start the application
main()
