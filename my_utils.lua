-- my_utils.lua - Utility functions for file operations and other common tasks

local my_utils = {}

-- Check if a file exists
function my_utils.fileExists(path)
    local file = io.open(path, "r")
    if file then
        file:close()
        return true
    end
    return false
end

-- Read content from a file
function my_utils.readFile(path)
    if not my_utils.fileExists(path) then
        return nil
    end
    
    local file = io.open(path, "r")
    if not file then
        return nil
    end
    
    local content = file:read("*a")
    file:close()
    return content
end

-- Write content to a file
function my_utils.writeToFile(path, content)
    local file = io.open(path, "w")
    if not file then
        return false
    end
    
    file:write(content)
    file:close()
    return true
end

-- Append content to a file
function my_utils.appendToFile(path, content)
    local file = io.open(path, "a+")
    if not file then
        return false
    end
    
    file:write(content)
    file:close()
    return true
end

-- Delete a file
function my_utils.deleteFile(path)
    if not my_utils.fileExists(path) then
        return true -- File doesn't exist, so consider it deleted
    end
    
    local success, err = os.remove(path)
    return success, err
end

-- Format a number to a specific number of decimal places
function my_utils.formatNumber(num, decimals)
    decimals = decimals or 2
    local mult = 10^decimals
    return math.floor(num * mult + 0.5) / mult
end

-- Get current timestamp in ISO format
function my_utils.getISOTimestamp()
    local time = os.date("*t")
    return string.format("%04d-%02d-%02dT%02d:%02d:%02d", 
        time.year, time.month, time.day, time.hour, time.min, time.sec)
end

return my_utils
