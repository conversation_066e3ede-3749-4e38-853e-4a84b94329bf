-- pin_module.lua - GPIO pin control functionality for Air780EG (LuatOS)

-- No module imports needed
-- Using the global gpio object that is available in LuatOS

local PinModule = {}

-- Initialize pins
function PinModule.setupPins()
    -- Define pin numbers based on the Air780EG schematic
    PinModule.pins = {
        -- Inputs
        S1 = 16,   -- Input signal 1 (GPIO 16)
        S2 = 18,   -- Input signal 2 (GPIO 18)
        S3 = 22,   -- Input signal 3 (GPIO 22)

        -- Outputs
        Relay1 = 2,  -- Relay 1 control (GPIO 2)
        Relay2 = 31, -- Relay 2 control (GPIO 31)
        Relay3 = 24, -- Relay 3 control (GPIO 24)
        Out3 = 17,   -- Output 3 (GPIO 17)
        Beep = 30,   -- Buzzer/Beeper (GPIO 30)
        Key1 = 26,   -- Key/Button 1 (GPIO 26)
        Key2 = 29,   -- Key/Button 2 (GPIO 29)

        -- Status LEDs
        -- Using available GPIO pins for LEDs
        CloudLED = 50, -- MQTT connection status LED (GPIO 50)
        NetLED = 49   -- Network status LED (GPIO 49)
    }

    -- Setup input pins with pull-up
    gpio.setup(PinModule.pins.S1, nil, gpio.PULLUP)  -- Input with pull-up
    gpio.setup(PinModule.pins.S2, nil, gpio.PULLUP)  -- Input with pull-up
    gpio.setup(PinModule.pins.S3, nil, gpio.PULLUP)  -- Input with pull-up

    -- Set all outputs to low initially
    PinModule.relayControl("Relay1", 0)
    PinModule.relayControl("Relay2", 0)
    PinModule.relayControl("Relay3", 0)
    PinModule.relayControl("Out3", 0)
    PinModule.relayControl("Key1", 0)
    PinModule.relayControl("Key2", 0)
    PinModule.relayControl("Beep", 0)

    -- Initialize status LEDs (off initially)
    PinModule.relayControl("CloudLED", 0)
    PinModule.relayControl("NetLED", 0)
end

-- Control relays or outputs
function PinModule.relayControl(pinName, state)
    local pinNum = PinModule.pins[pinName]
    if pinNum then
        -- Set pin as output with the specified state (0 or 1)
        gpio.setup(pinNum, state)
    else
        print("Error: Pin " .. pinName .. " is not defined.")
    end
end

-- Read pin state
function PinModule.readPinState(pinName)
    local pinNum = PinModule.pins[pinName]
    if pinNum then
        return gpio.get(pinNum)
    else
        print("Error: Pin " .. pinName .. " is not defined.")
        return nil
    end
end

-- Control Cloud LED
function PinModule.setCloudLED(state)
    PinModule.relayControl("CloudLED", state)
end

-- Control Network LED
function PinModule.setNetLED(state)
    PinModule.relayControl("NetLED", state)
end

-- Control Beeper/Buzzer
function PinModule.beep(duration_ms)
    duration_ms = duration_ms or 200  -- Default 200ms beep
    PinModule.relayControl("Beep", 1)  -- Turn on beeper
    -- Note: In a real implementation, you would use a timer to turn it off
    -- For now, we'll rely on the calling code to manage timing
end

-- Turn off beeper
function PinModule.beepOff()
    PinModule.relayControl("Beep", 0)
end

-- Beep pattern function (beep multiple times)
function PinModule.beepPattern(count, beep_duration, pause_duration)
    count = count or 1
    beep_duration = beep_duration or 200
    pause_duration = pause_duration or 200

    -- This function will be called from a task context where sys.wait is available
    for i = 1, count do
        PinModule.relayControl("Beep", 1)  -- Turn on beeper
        if sys and sys.wait then
            sys.wait(beep_duration)  -- Beep duration
        end
        PinModule.relayControl("Beep", 0)  -- Turn off beeper

        -- Add pause between beeps (except after the last beep)
        if i < count and sys and sys.wait then
            sys.wait(pause_duration)
        end
    end
end

return PinModule