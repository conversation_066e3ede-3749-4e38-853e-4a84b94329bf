-- pin_module.lua - GPIO pin control functionality for Air780EG (LuatOS)

-- No module imports needed
-- Using the global gpio object that is available in LuatOS

local PinModule = {}

-- Initialize pins
function PinModule.setupPins()
    -- Define pin numbers based on the Air780EG schematic
    PinModule.pins = {
        -- Inputs
        S1 = 16,   -- Input signal 1 (GPIO 16)
        S2 = 18,   -- Input signal 2 (GPIO 18)
        S3 = 22,   -- Input signal 3 (GPIO 22)

        -- Outputs
        Relay1 = 2,  -- Relay 1 control (GPIO 2)
        Relay2 = 31, -- Relay 2 control (GPIO 31)
        Relay3 = 24, -- Relay 3 control (GPIO 24)
        Out3 = 17,   -- Output 3 (GPIO 17)
        Beep = 30,   -- Buzzer/Beeper (GPIO 30)
        Key1 = 26,   -- Key/Button 1 (GPIO 26)
        Key2 = 29,   -- Key/Button 2 (GPIO 29)

        -- Status LEDs
        -- Using available GPIO pins for LEDs
        CloudLED = 1, -- MQTT connection status LED (GPIO 1)
        NetLED = 27   -- Network status LED (GPIO 27)
    }

    -- Setup input pins with pull-up
    gpio.setup(PinModule.pins.S1, nil, gpio.PULLUP)  -- Input with pull-up
    gpio.setup(PinModule.pins.S2, nil, gpio.PULLUP)  -- Input with pull-up
    gpio.setup(PinModule.pins.S3, nil, gpio.PULLUP)  -- Input with pull-up

    -- Set all outputs to low initially
    PinModule.relayControl("Relay1", 0)
    PinModule.relayControl("Relay2", 0)
    PinModule.relayControl("Relay3", 0)
    PinModule.relayControl("Out3", 0)
    PinModule.relayControl("Key1", 0)
    PinModule.relayControl("Key2", 0)
    PinModule.relayControl("Beep", 0)

    -- Initialize status LEDs (off initially)
    PinModule.relayControl("CloudLED", 0)
    PinModule.relayControl("NetLED", 0)
end

-- Control relays or outputs
function PinModule.relayControl(pinName, state)
    local pinNum = PinModule.pins[pinName]
    if pinNum then
        -- Set pin as output with the specified state (0 or 1)
        gpio.setup(pinNum, state)
    else
        print("Error: Pin " .. pinName .. " is not defined.")
    end
end

-- Read pin state
function PinModule.readPinState(pinName)
    local pinNum = PinModule.pins[pinName]
    if pinNum then
        return gpio.get(pinNum)
    else
        print("Error: Pin " .. pinName .. " is not defined.")
        return nil
    end
end

-- Control Cloud LED
function PinModule.setCloudLED(state)
    PinModule.relayControl("CloudLED", state)
end

-- Control Network LED
function PinModule.setNetLED(state)
    PinModule.relayControl("NetLED", state)
end

return PinModule