-- pin_module.lua - GPIO pin control functionality for Air780EG (LuatOS)

-- No module imports needed
-- Using the global gpio object that is available in LuatOS

local PinModule = {}

-- Initialize pins
function PinModule.setupPins()
    -- Define pin numbers based on the Air780EG schematic
    PinModule.pins = {
        -- Inputs
        S1 = 16,   -- Input signal 1 (GPIO 16)
        S2 = 18,   -- Input signal 2 (GPIO 18)
        S3 = 22,   -- Input signal 3 (GPIO 22)

        -- Outputs
        Relay1 = 2,  -- Relay 1 control (GPIO 2)
        Relay2 = 31, -- Relay 2 control (GPIO 31)
        Relay3 = 24, -- Relay 3 control (GPIO 24)
        Out3 = 30,   -- Output 3 (GPIO 30)
        Beep = 4,    -- Passive Buzzer/Beeper (PWM4 - supports PWM)
        Key1 = 26,   -- Key/Button 1 (GPIO 26)
        Key2 = 29,   -- Key/Button 2 (GPIO 29)

        -- Status LEDs
        -- Using available GPIO pins for LEDs
        CloudLED = 1, -- MQTT connection status LED (GPIO 1)
        NetLED = 9   -- Network status LED (GPIO 9)
    }

    -- Setup input pins with pull-up
    gpio.setup(PinModule.pins.S1, nil, gpio.PULLUP)  -- Input with pull-up
    gpio.setup(PinModule.pins.S2, nil, gpio.PULLUP)  -- Input with pull-up
    gpio.setup(PinModule.pins.S3, nil, gpio.PULLUP)  -- Input with pull-up

    -- Set all outputs to low initially
    PinModule.relayControl("Relay1", 0)
    PinModule.relayControl("Relay2", 0)
    PinModule.relayControl("Relay3", 0)
    PinModule.relayControl("Out3", 0)
    PinModule.relayControl("Key1", 0)
    PinModule.relayControl("Key2", 0)
    -- Initialize passive buzzer (PWM4) - ensure it's off initially
    -- No need to initialize PWM here, it will be done when needed

    -- Initialize status LEDs (off initially)
    PinModule.relayControl("CloudLED", 0)
    PinModule.relayControl("NetLED", 0)
end

-- Control relays or outputs
function PinModule.relayControl(pinName, state)
    local pinNum = PinModule.pins[pinName]
    if pinNum then
        -- Set pin as output with the specified state (0 or 1)
        gpio.setup(pinNum, state)
    else
        print("Error: Pin " .. pinName .. " is not defined.")
    end
end

-- Read pin state
function PinModule.readPinState(pinName)
    local pinNum = PinModule.pins[pinName]
    if pinNum then
        return gpio.get(pinNum)
    else
        print("Error: Pin " .. pinName .. " is not defined.")
        return nil
    end
end

-- Control Cloud LED
function PinModule.setCloudLED(state)
    PinModule.relayControl("CloudLED", state)
end

-- Control Network LED
function PinModule.setNetLED(state)
    PinModule.relayControl("NetLED", state)
end

-- Control Passive Buzzer/Beeper using PWM
function PinModule.beep(frequency, duration_ms)
    frequency = frequency or 1000  -- Default 1kHz frequency
    duration_ms = duration_ms or 200  -- Default 200ms beep

    -- Start PWM with 50% duty cycle for the passive buzzer
    pwm.open(PinModule.pins.Beep, frequency, 50)

    -- If sys.wait is available, wait for the duration and then stop
    if sys and sys.wait then
        sys.wait(duration_ms)
        pwm.stop(PinModule.pins.Beep)
    end
    -- Note: If sys.wait is not available, caller must call beepOff() manually
end

-- Turn off beeper
function PinModule.beepOff()
    pwm.stop(PinModule.pins.Beep)
end

-- Start beeper without auto-stop (caller must call beepOff)
function PinModule.beepOn(frequency)
    frequency = frequency or 1000  -- Default 1kHz frequency
    pwm.open(PinModule.pins.Beep, frequency, 50)
end

-- Beep pattern function (beep multiple times) - for passive buzzer
function PinModule.beepPattern(count, beep_duration, pause_duration, frequency)
    count = count or 1
    beep_duration = beep_duration or 200
    pause_duration = pause_duration or 200
    frequency = frequency or 1000  -- Default 1kHz frequency

    -- This function will be called from a task context where sys.wait is available
    for i = 1, count do
        -- Start PWM for beep
        pwm.open(PinModule.pins.Beep, frequency, 50)
        if sys and sys.wait then
            sys.wait(beep_duration)  -- Beep duration
        end
        -- Stop PWM
        pwm.stop(PinModule.pins.Beep)

        -- Add pause between beeps (except after the last beep)
        if i < count and sys and sys.wait then
            sys.wait(pause_duration)
        end
    end
end

return PinModule