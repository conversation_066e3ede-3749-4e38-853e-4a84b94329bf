-- pin_module.lua - GPIO pin control functionality for Air780EG (LuatOS)

-- No module imports needed
-- Using the global gpio object that is available in LuatOS

local PinModule = {}

-- Initialize pins
function PinModule.setupPins()
    -- Define pin numbers based on the Air780EG schematic
    PinModule.pins = {
        -- Inputs
        S1 = 16,   -- Input signal 1 (GPIO 16)
        S2 = 18,   -- Input signal 2 (GPIO 18)
        S3 = 22,   -- Input signal 3 (GPIO 22)

        -- Outputs
        Relay1 = 2,  -- Relay 1 control (GPIO 2)
        Relay2 = 31, -- Relay 2 control (GPIO 31)
        Relay3 = 24, -- Relay 3 control (GPIO 24)
        Out3 = 30,   -- Output 3 (GPIO 30)
        Beep = 17,   -- Passive Buzzer/Beeper (GPIO 17 - custom software PWM)
        Key1 = 26,   -- Key/Button 1 (GPIO 26)
        Key2 = 29,   -- Key/Button 2 (GPIO 29)

        -- Status LEDs
        -- Using available GPIO pins for LEDs
        CloudLED = 1, -- MQTT connection status LED (GPIO 1)
        NetLED = 9   -- Network status LED (GPIO 9)
    }

    -- Setup input pins with pull-up
    gpio.setup(PinModule.pins.S1, nil, gpio.PULLUP)  -- Input with pull-up
    gpio.setup(PinModule.pins.S2, nil, gpio.PULLUP)  -- Input with pull-up
    gpio.setup(PinModule.pins.S3, nil, gpio.PULLUP)  -- Input with pull-up

    -- Set all outputs to low initially
    PinModule.relayControl("Relay1", 0)
    PinModule.relayControl("Relay2", 0)
    PinModule.relayControl("Relay3", 0)
    PinModule.relayControl("Out3", 0)
    PinModule.relayControl("Key1", 0)
    PinModule.relayControl("Key2", 0)
    -- Initialize passive buzzer (PWM4) - ensure it's off initially
    -- No need to initialize PWM here, it will be done when needed

    -- Initialize status LEDs (off initially)
    PinModule.relayControl("CloudLED", 0)
    PinModule.relayControl("NetLED", 0)
end

-- Control relays or outputs
function PinModule.relayControl(pinName, state)
    local pinNum = PinModule.pins[pinName]
    if pinNum then
        -- Set pin as output with the specified state (0 or 1)
        gpio.setup(pinNum, state)
    else
        print("Error: Pin " .. pinName .. " is not defined.")
    end
end

-- Read pin state
function PinModule.readPinState(pinName)
    local pinNum = PinModule.pins[pinName]
    if pinNum then
        return gpio.get(pinNum)
    else
        print("Error: Pin " .. pinName .. " is not defined.")
        return nil
    end
end

-- Control Cloud LED
function PinModule.setCloudLED(state)
    PinModule.relayControl("CloudLED", state)
end

-- Control Network LED
function PinModule.setNetLED(state)
    PinModule.relayControl("NetLED", state)
end

-- Global variable to control software PWM
PinModule.beep_running = false

-- Software PWM implementation for passive buzzer on pin 17
function PinModule.softwarePWM(frequency, duration_ms, duty_cycle)
    frequency = frequency or 1000  -- Default 1kHz frequency
    duration_ms = duration_ms or 200  -- Default 200ms duration
    duty_cycle = duty_cycle or 50  -- Default 50% duty cycle

    -- Calculate timing for software PWM
    local period_us = math.floor(1000000 / frequency)  -- Period in microseconds
    local high_time_us = math.floor(period_us * duty_cycle / 100)  -- High time in microseconds
    local low_time_us = period_us - high_time_us  -- Low time in microseconds

    -- Convert to milliseconds for sys.wait (minimum 1ms)
    local high_time_ms = math.max(1, math.floor(high_time_us / 1000))
    local low_time_ms = math.max(1, math.floor(low_time_us / 1000))

    -- Calculate number of cycles
    local cycle_time_ms = high_time_ms + low_time_ms
    local cycles = math.floor(duration_ms / cycle_time_ms)

    -- Set pin as output
    gpio.setup(PinModule.pins.Beep, 0)

    -- Generate software PWM
    PinModule.beep_running = true
    for i = 1, cycles do
        if not PinModule.beep_running then
            break  -- Allow early termination
        end

        -- High phase
        gpio.set(PinModule.pins.Beep, 1)
        if sys and sys.wait then
            sys.wait(high_time_ms)
        end

        -- Low phase
        gpio.set(PinModule.pins.Beep, 0)
        if sys and sys.wait then
            sys.wait(low_time_ms)
        end
    end

    -- Ensure pin is low when done
    gpio.set(PinModule.pins.Beep, 0)
    PinModule.beep_running = false
end

-- Simple beep function using software PWM
function PinModule.beep(frequency, duration_ms)
    frequency = frequency or 1000  -- Default 1kHz frequency
    duration_ms = duration_ms or 200  -- Default 200ms beep

    PinModule.softwarePWM(frequency, duration_ms, 50)
end

-- Turn off beeper (stop software PWM)
function PinModule.beepOff()
    PinModule.beep_running = false
    gpio.set(PinModule.pins.Beep, 0)
end

-- Start continuous beeper (must be stopped manually)
function PinModule.beepOn(frequency)
    frequency = frequency or 1000  -- Default 1kHz frequency

    -- Start a continuous software PWM in a task
    if sys and sys.taskInit then
        sys.taskInit(function()
            PinModule.beep_running = true
            gpio.setup(PinModule.pins.Beep, 0)

            -- Calculate timing
            local period_us = math.floor(1000000 / frequency)
            local high_time_ms = math.max(1, math.floor(period_us / 2000))  -- 50% duty cycle
            local low_time_ms = high_time_ms

            while PinModule.beep_running do
                gpio.set(PinModule.pins.Beep, 1)
                sys.wait(high_time_ms)
                gpio.set(PinModule.pins.Beep, 0)
                sys.wait(low_time_ms)
            end

            gpio.set(PinModule.pins.Beep, 0)  -- Ensure off when stopped
        end)
    end
end

-- Beep pattern function (beep multiple times) - using software PWM
function PinModule.beepPattern(count, beep_duration, pause_duration, frequency)
    count = count or 1
    beep_duration = beep_duration or 200
    pause_duration = pause_duration or 200
    frequency = frequency or 1000  -- Default 1kHz frequency

    -- This function will be called from a task context where sys.wait is available
    for i = 1, count do
        -- Generate software PWM for beep
        PinModule.softwarePWM(frequency, beep_duration, 50)

        -- Add pause between beeps (except after the last beep)
        if i < count and sys and sys.wait then
            sys.wait(pause_duration)
        end
    end
end

return PinModule